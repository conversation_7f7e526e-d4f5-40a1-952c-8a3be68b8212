"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
  NavigationMenuTrigger,
  NavigationMenuContent,
  NavigationMenuLink,
} from "@/components/ui/navigation-menu";
import { Button } from "@/components/ui/button";
import {
  ArrowRightIcon,
  UserCircle,
  MenuIcon,
  X as CloseIcon,
} from "lucide-react";
import navLinks from "@/data/navlinks.json";
import Wrapper from "@/components/Wrapper";

const Navbar = () => {
  const [mobileOpen, setMobileOpen] = useState(false);

  return (
    <Wrapper>
      <header className="px-4 py-6 md:py-8 w-full bg-transparent relative">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link href="/" className="z-20">
            <Image
              src="/Logo.png"
              alt="Aarogya Global Logo"
              width={149}
              height={39}
              className="h-auto w-auto"
              priority
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center ">
            <NavigationMenu>
              <NavigationMenuList className="gap-2">
                {navLinks.map((link) =>
                  link.dropdown ? (
                    <NavigationMenuItem key={link.label}>
                      <NavigationMenuTrigger
                        className={`text-white bg-transparent hover:bg-transparent hover:text-[#04CE78] 
                          focus:text-[#04CE78] data-[state=open]:text-[#04CE78] px-3 py-2 font-semibold`}
                      >
                        {link.label}
                      </NavigationMenuTrigger>
                      <NavigationMenuContent className="bg-white min-w-[180px] rounded-md shadow-lg">
                        <ul className="py-2">
                          {link.dropdown.map((item) => (
                            <li key={item.label}>
                              <NavigationMenuLink asChild>
                                <Link
                                  href={item.href}
                                  className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                                >
                                  {item.label}
                                </Link>
                              </NavigationMenuLink>
                            </li>
                          ))}
                        </ul>
                      </NavigationMenuContent>
                    </NavigationMenuItem>
                  ) : (
                    <NavigationMenuItem key={link.label}>
                      <NavigationMenuLink asChild>
                        <Link
                          href={link.href}
                          className={`px-3 py-2 font-semibold transition-colors rounded text-white 
                            hover:text-[#04CE78] focus:text-[#04CE78] ${
                              link.highlight ? "text-[#04CE78]" : ""
                            }`}
                        >
                          {link.label}
                        </Link>
                      </NavigationMenuLink>
                    </NavigationMenuItem>
                  )
                )}
              </NavigationMenuList>
            </NavigationMenu>
          </nav>

          {/* Desktop Auth & CTA */}
          <div className="hidden md:flex items-center gap-4">
            <Link
              href="/login"
              className="flex items-center gap-2 text-white hover:text-[#04CE78] font-semibold transition-colors"
            >
              <UserCircle className="w-5 h-5" />
              Login/Register
            </Link>
            <Button
              asChild
              className="bg-[#04CE78] hover:bg-[#03b86a] text-white font-bold px-6 py-6 rounded-lg shadow"
            >
              <Link href="/appointment" className="flex items-center gap-2">
                Make An Appointment <ArrowRightIcon className="w-4 h-4" />
              </Link>
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-white z-20 p-2"
            onClick={() => setMobileOpen(!mobileOpen)}
            aria-label="Toggle menu"
          >
            {mobileOpen ? (
              <CloseIcon className="w-6 h-6" />
            ) : (
              <MenuIcon className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Mobile Menu */}
        {mobileOpen && (
          <>
            <div
              className="fixed inset-0 bg-black/50 z-10 md:hidden"
              onClick={() => setMobileOpen(false)}
            />
            <div className="fixed top-0 left-0 w-full h-screen bg-[#1a0856] z-20 pt-24 px-6 flex flex-col items-center gap-8 overflow-y-auto">
              <NavigationMenu className="w-full" orientation="vertical">
                <NavigationMenuList className="flex flex-col gap-2 w-full">
                  {navLinks.map((link) =>
                    link.dropdown ? (
                      <NavigationMenuItem key={link.label} className="w-full">
                        <NavigationMenuTrigger
                          className={`text-white bg-transparent w-full justify-between px-4 py-3 font-semibold ${
                            mobileOpen ? "border-b border-white/20" : ""
                          }`}
                        >
                          {link.label}
                        </NavigationMenuTrigger>
                        <NavigationMenuContent className="w-full bg-white rounded-md shadow-lg">
                          <ul className="py-2">
                            {link.dropdown.map((item) => (
                              <li key={item.label}>
                                <NavigationMenuLink asChild>
                                  <Link
                                    href={item.href}
                                    className="block px-4 py-2 text-gray-700 hover:bg-gray-100"
                                    onClick={() => setMobileOpen(false)}
                                  >
                                    {item.label}
                                  </Link>
                                </NavigationMenuLink>
                              </li>
                            ))}
                          </ul>
                        </NavigationMenuContent>
                      </NavigationMenuItem>
                    ) : (
                      <NavigationMenuItem key={link.label} className="w-full">
                        <NavigationMenuLink asChild>
                          <Link
                            href={link.href}
                            className={`block px-4 py-3 font-semibold transition-colors rounded text-white ${
                              link.highlight ? "text-[#04CE78]" : ""
                            } ${mobileOpen ? "border-b border-white/20" : ""}`}
                            onClick={() => setMobileOpen(false)}
                          >
                            {link.label}
                          </Link>
                        </NavigationMenuLink>
                      </NavigationMenuItem>
                    )
                  )}
                </NavigationMenuList>
              </NavigationMenu>

              <div className="flex flex-col items-center gap-4 w-full max-w-xs mt-4">
                <Link
                  href="/login"
                  className="flex items-center justify-center gap-2 text-white hover:text-cyan-300 font-semibold transition-colors w-full py-2"
                  onClick={() => setMobileOpen(false)}
                >
                  <UserCircle className="w-5 h-5" />
                  Login/Register
                </Link>
                <Button
                  asChild
                  className="bg-[#04CE78] hover:bg-[#03b86a] text-white font-bold px-6 py-3 rounded-lg shadow w-full"
                >
                  <Link
                    href="/appointment"
                    className="flex items-center justify-center gap-2"
                    onClick={() => setMobileOpen(false)}
                  >
                    Make An Appointment <ArrowRightIcon className="w-4 h-4" />
                  </Link>
                </Button>
              </div>
            </div>
          </>
        )}
      </header>
    </Wrapper>
  );
};

export default Navbar;
